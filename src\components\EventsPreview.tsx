import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Calendar, Music, BookOpen, Palette } from 'lucide-react';
import './EventsPreview.css';

// Define Event type locally
interface Event {
  id: string;
  title: string;
  description: string;
  date: Date;
  time: string;
  type: string;
  image?: string;
  rsvpRequired: boolean;
  maxAttendees?: number;
  currentAttendees?: number;
}

const EventsPreview: React.FC = () => {
  // Sample events - inspired by real indie café events
  const upcomingEvents: Event[] = [
    {
      id: '1',
      title: 'SPEAK OUT - Spoken Word Open Mic',
      description: 'The legendary spoken word night returns. Come see some of the best poets on the scene. Fancy a go yourself? Get your name down.',
      date: new Date('2024-09-18'),
      time: '6:30 PM - 10:00 PM',
      type: 'poetry',
      rsvpRequired: false,
    },
    {
      id: '2',
      title: 'FOLK JAM SESSION',
      description: 'Every final Wednesday of the month - informal acoustic folk jam. Bring your own instruments and singing voices.',
      date: new Date('2024-09-24'),
      time: '7:00 PM - 10:00 PM',
      type: 'music',
      rsvpRequired: false,
    },
    {
      id: '3',
      title: 'LOCAL BAND SHOWCASE',
      description: 'Supporting emerging artists and developing our local music scene. Intimate venue with lively, respectful crowds.',
      date: new Date('2024-09-30'),
      time: '8:00 PM - 11:00 PM',
      type: 'music',
      rsvpRequired: true,
    },
  ];

  const getEventIcon = (type: string) => {
    switch (type) {
      case 'music':
        return <Music size={20} />;
      case 'poetry':
        return <BookOpen size={20} />;
      case 'art':
        return <Palette size={20} />;
      default:
        return <Calendar size={20} />;
    }
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
    });
  };

  return (
    <section className="events-preview section bg-green">
      <div className="container">
        <div className="section-header text-center">
          <h2 style={{ color: 'var(--color-white)' }}>WHAT'S ON @ INDIE</h2>
          <p style={{ color: 'var(--color-cream-beige)' }}>
            Live music across all genres | Open mics | Community events
          </p>
        </div>
        
        <div className="events-grid">
          {upcomingEvents.map((event) => (
            <div key={event.id} className="event-card">
              <div className="event-icon">
                {getEventIcon(event.type)}
              </div>
              <div className="event-info">
                <h3>{event.title}</h3>
                <p>{event.description}</p>
                <div className="event-details">
                  <span className="event-date">{formatDate(event.date)}</span>
                  <span className="event-time">{event.time}</span>
                </div>
                {event.rsvpRequired && (
                  <button className="btn btn-primary event-rsvp">
                    RSVP
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>
        
        <div className="text-center">
          <Link to="/events" className="btn btn-primary">
            View All Events
          </Link>
        </div>
      </div>
    </section>
  );
};

export default EventsPreview;
