import React from 'react';
import { Link } from 'react-router-dom';
import './MenuPreview.css';

// Define MenuItem type locally
interface MenuItem {
  id: string;
  name: string;
  description: string;
  price: number;
  category: string;
  image?: string;
  isVegan?: boolean;
  isGlutenFree?: boolean;
  isSpecial?: boolean;
}

const MenuPreview: React.FC = () => {
  // Sample menu items - replace with actual data
  const featuredItems: MenuItem[] = [
    {
      id: '1',
      name: 'Signature Blend',
      description: 'Our house special with notes of chocolate and caramel',
      price: 4.50,
      category: 'coffee',
      isSpecial: true,
    },
    {
      id: '2',
      name: 'Artisan Croissant',
      description: 'Buttery, flaky pastry baked fresh daily',
      price: 3.25,
      category: 'pastries',
    },
    {
      id: '3',
      name: 'Avocado Toast',
      description: 'Smashed avocado on sourdough with hemp seeds',
      price: 8.75,
      category: 'breakfast',
      isVegan: true,
    },
  ];

  return (
    <section className="menu-preview section">
      <div className="container">
        <div className="section-header text-center">
          <h2>Featured Menu</h2>
          <p>Taste the difference in every sip and bite</p>
        </div>
        
        <div className="featured-items">
          {featuredItems.map((item) => (
            <div key={item.id} className="menu-item-card">
              <div className="item-image">
                <div className="image-placeholder">{item.name}</div>
                {item.isSpecial && <span className="special-badge">Daily Special</span>}
                {item.isVegan && <span className="vegan-badge">Vegan</span>}
              </div>
              <div className="item-info">
                <h3>{item.name}</h3>
                <p>{item.description}</p>
                <div className="item-price">${item.price.toFixed(2)}</div>
              </div>
            </div>
          ))}
        </div>
        
        <div className="text-center">
          <Link to="/menu" className="btn btn-secondary">
            View Full Menu
          </Link>
        </div>
      </div>
    </section>
  );
};

export default MenuPreview;
