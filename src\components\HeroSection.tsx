import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import './HeroSection.css';

const HeroSection: React.FC = () => {
  return (
    <section className="hero-section">
      <div className="hero-background">
        {/* Placeholder for background image - you can replace with actual café image */}
        <div className="hero-overlay"></div>
      </div>
      
      <div className="hero-content">
        <motion.div
          className="hero-text"
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          <h1 className="hero-title">INDIE CAFÉ</h1>
          <p className="hero-subtitle">COFFEE | MUSIC | COMMUNITY</p>
          <p className="hero-description">
            A co-operative in the heart of the neighborhood serving the town's best food and drink.
            We host local bands, support emerging artists, and champion community connection.
          </p>

          <motion.div
            className="hero-buttons"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
          >
            <Link to="/menu" className="btn btn-primary hero-cta">
              GRAB A COFFEE
            </Link>
            <Link to="/events" className="btn btn-secondary hero-cta">
              WHAT'S ON
            </Link>
          </motion.div>
        </motion.div>
      </div>

      {/* Parallax coffee steam animation */}
      <div className="hero-decoration">
        <motion.div
          className="steam-line steam-1"
          animate={{
            y: [-20, -40, -20],
            opacity: [0.3, 0.7, 0.3],
          }}
          transition={{
            duration: 3,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />
        <motion.div
          className="steam-line steam-2"
          animate={{
            y: [-15, -35, -15],
            opacity: [0.4, 0.8, 0.4],
          }}
          transition={{
            duration: 2.5,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 0.5,
          }}
        />
        <motion.div
          className="steam-line steam-3"
          animate={{
            y: [-25, -45, -25],
            opacity: [0.2, 0.6, 0.2],
          }}
          transition={{
            duration: 3.5,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 1,
          }}
        />
      </div>
    </section>
  );
};

export default HeroSection;
