import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Menu, X, ChevronDown } from 'lucide-react';
import './Navigation.css';

// Define NavItem type locally to avoid import issues
interface NavItem {
  label: string;
  href: string;
  dropdown?: NavItem[];
}

const Navigation: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);
  const location = useLocation();

  const navItems: NavItem[] = [
    { label: 'Home', href: '/' },
    { 
      label: 'Menu', 
      href: '/menu',
      dropdown: [
        { label: 'Coffee', href: '/menu?category=coffee' },
        { label: 'Pastries', href: '/menu?category=pastries' },
        { label: 'Breakfast', href: '/menu?category=breakfast' },
        { label: 'Lunch', href: '/menu?category=lunch' },
        { label: 'Vegan Options', href: '/menu?category=vegan' },
      ]
    },
    { label: 'About Us', href: '/about' },
    { label: 'Events', href: '/events' },
    { label: 'Contact', href: '/contact' },
  ];

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const handleDropdownToggle = (label: string) => {
    setActiveDropdown(activeDropdown === label ? null : label);
  };

  const closeMenu = () => {
    setIsMenuOpen(false);
    setActiveDropdown(null);
  };

  return (
    <nav className={`navigation ${isScrolled ? 'scrolled' : ''}`}>
      <div className="nav-container">
        <Link to="/" className="nav-logo" onClick={closeMenu}>
          <h2>Indie Café</h2>
        </Link>

        {/* Desktop Navigation */}
        <ul className="nav-menu desktop-menu">
          {navItems.map((item) => (
            <li key={item.label} className="nav-item">
              {item.dropdown ? (
                <div className="dropdown">
                  <button
                    className={`nav-link dropdown-toggle ${
                      location.pathname === item.href ? 'active' : ''
                    }`}
                    onClick={() => handleDropdownToggle(item.label)}
                  >
                    {item.label}
                    <ChevronDown size={16} />
                  </button>
                  {activeDropdown === item.label && (
                    <ul className="dropdown-menu">
                      {item.dropdown.map((dropdownItem) => (
                        <li key={dropdownItem.label}>
                          <Link
                            to={dropdownItem.href}
                            className="dropdown-link"
                            onClick={closeMenu}
                          >
                            {dropdownItem.label}
                          </Link>
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
              ) : (
                <Link
                  to={item.href}
                  className={`nav-link ${
                    location.pathname === item.href ? 'active' : ''
                  }`}
                  onClick={closeMenu}
                >
                  {item.label}
                </Link>
              )}
            </li>
          ))}
        </ul>

        {/* Mobile Menu Toggle */}
        <button className="mobile-menu-toggle" onClick={toggleMenu}>
          {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
        </button>

        {/* Mobile Navigation */}
        <ul className={`nav-menu mobile-menu ${isMenuOpen ? 'open' : ''}`}>
          {navItems.map((item) => (
            <li key={item.label} className="nav-item">
              {item.dropdown ? (
                <div className="dropdown">
                  <button
                    className={`nav-link dropdown-toggle ${
                      location.pathname === item.href ? 'active' : ''
                    }`}
                    onClick={() => handleDropdownToggle(item.label)}
                  >
                    {item.label}
                    <ChevronDown size={16} />
                  </button>
                  {activeDropdown === item.label && (
                    <ul className="dropdown-menu">
                      {item.dropdown.map((dropdownItem) => (
                        <li key={dropdownItem.label}>
                          <Link
                            to={dropdownItem.href}
                            className="dropdown-link"
                            onClick={closeMenu}
                          >
                            {dropdownItem.label}
                          </Link>
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
              ) : (
                <Link
                  to={item.href}
                  className={`nav-link ${
                    location.pathname === item.href ? 'active' : ''
                  }`}
                  onClick={closeMenu}
                >
                  {item.label}
                </Link>
              )}
            </li>
          ))}
        </ul>
      </div>
    </nav>
  );
};

export default Navigation;
