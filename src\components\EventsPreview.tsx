import React from 'react';
import { Link } from 'react-router-dom';
import { Calendar, Music, BookOpen, Palette } from 'lucide-react';
import { Event } from '../types';
import './EventsPreview.css';

const EventsPreview: React.FC = () => {
  // Sample events - replace with actual data
  const upcomingEvents: Event[] = [
    {
      id: '1',
      title: 'Acoustic Night',
      description: 'Local musicians perform in our cozy corner',
      date: new Date('2024-09-15'),
      time: '7:00 PM',
      type: 'music',
      rsvpRequired: true,
    },
    {
      id: '2',
      title: 'Poetry Reading',
      description: 'Open mic for poets and storytellers',
      date: new Date('2024-09-20'),
      time: '6:30 PM',
      type: 'poetry',
      rsvpRequired: false,
    },
    {
      id: '3',
      title: 'Art Exhibition Opening',
      description: 'Featuring local artists from the community',
      date: new Date('2024-09-25'),
      time: '5:00 PM',
      type: 'art',
      rsvpRequired: true,
    },
  ];

  const getEventIcon = (type: string) => {
    switch (type) {
      case 'music':
        return <Music size={20} />;
      case 'poetry':
        return <BookOpen size={20} />;
      case 'art':
        return <Palette size={20} />;
      default:
        return <Calendar size={20} />;
    }
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
    });
  };

  return (
    <section className="events-preview section bg-green">
      <div className="container">
        <div className="section-header text-center">
          <h2 style={{ color: 'var(--color-white)' }}>Upcoming Events</h2>
          <p style={{ color: 'var(--color-cream-beige)' }}>
            Join us for music, art, and community gatherings
          </p>
        </div>
        
        <div className="events-grid">
          {upcomingEvents.map((event) => (
            <div key={event.id} className="event-card">
              <div className="event-icon">
                {getEventIcon(event.type)}
              </div>
              <div className="event-info">
                <h3>{event.title}</h3>
                <p>{event.description}</p>
                <div className="event-details">
                  <span className="event-date">{formatDate(event.date)}</span>
                  <span className="event-time">{event.time}</span>
                </div>
                {event.rsvpRequired && (
                  <button className="btn btn-primary event-rsvp">
                    RSVP
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>
        
        <div className="text-center">
          <Link to="/events" className="btn btn-primary">
            View All Events
          </Link>
        </div>
      </div>
    </section>
  );
};

export default EventsPreview;
