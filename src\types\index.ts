// Menu item types
export interface MenuItem {
  id: string;
  name: string;
  description: string;
  price: number;
  category: MenuCategory;
  image?: string;
  isVegan?: boolean;
  isGlutenFree?: boolean;
  isSpecial?: boolean;
}

export type MenuCategory = 'coffee' | 'pastries' | 'breakfast' | 'lunch' | 'beverages' | 'desserts';

// Event types
export interface Event {
  id: string;
  title: string;
  description: string;
  date: Date;
  time: string;
  type: EventType;
  image?: string;
  rsvpRequired: boolean;
  maxAttendees?: number;
  currentAttendees?: number;
}

export type EventType = 'music' | 'poetry' | 'art' | 'workshop' | 'community' | 'special';

// Contact form types
export interface ContactFormData {
  name: string;
  email: string;
  message: string;
  inquiryType: InquiryType;
}

export type InquiryType = 'general' | 'catering' | 'events' | 'feedback' | 'partnership';

// Newsletter signup
export interface NewsletterSignup {
  email: string;
}

// Navigation types
export interface NavItem {
  label: string;
  href: string;
  dropdown?: NavItem[];
}

// Social media links
export interface SocialLink {
  platform: string;
  url: string;
  icon: string;
}

// Café information
export interface CafeInfo {
  name: string;
  tagline: string;
  description: string;
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
  };
  phone: string;
  email: string;
  hours: {
    [key: string]: string;
  };
  socialLinks: SocialLink[];
}
