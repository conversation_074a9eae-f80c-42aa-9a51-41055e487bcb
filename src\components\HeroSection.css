.hero-section {
  position: relative;
  height: calc(100vh - 70px);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--color-primary-green) 0%, var(--color-light-green) 100%);
  /* Placeholder gradient - replace with actual café image */
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 800"><defs><pattern id="cafe-pattern" x="0" y="0" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100%" height="100%" fill="url(%23cafe-pattern)"/></svg>');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(47, 106, 72, 0.7);
  background: linear-gradient(
    135deg,
    rgba(47, 106, 72, 0.8) 0%,
    rgba(47, 106, 72, 0.6) 100%
  );
}

.hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
  color: var(--color-white);
  max-width: 800px;
  padding: 0 var(--spacing-md);
}

.hero-title {
  font-size: 4rem;
  font-weight: 700;
  margin-bottom: var(--spacing-md);
  color: var(--color-white);
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  line-height: 1.1;
}

.hero-subtitle {
  font-size: 1.5rem;
  font-weight: 300;
  margin-bottom: var(--spacing-xl);
  color: var(--color-cream-beige);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.hero-cta {
  font-size: 1.2rem;
  padding: var(--spacing-md) var(--spacing-xl);
  box-shadow: var(--shadow-lg);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 600;
}

.hero-cta:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 25px -5px rgba(0, 0, 0, 0.2);
}

/* Decorative steam animation */
.hero-decoration {
  position: absolute;
  bottom: 20%;
  right: 10%;
  z-index: 1;
}

.steam-line {
  position: absolute;
  width: 3px;
  height: 40px;
  background: linear-gradient(
    to top,
    transparent 0%,
    rgba(255, 255, 255, 0.6) 50%,
    transparent 100%
  );
  border-radius: 2px;
}

.steam-1 {
  left: 0;
}

.steam-2 {
  left: 8px;
}

.steam-3 {
  left: 16px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }
  
  .hero-subtitle {
    font-size: 1.2rem;
  }
  
  .hero-cta {
    font-size: 1rem;
    padding: var(--spacing-sm) var(--spacing-lg);
  }
  
  .hero-decoration {
    display: none;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 2rem;
  }
  
  .hero-subtitle {
    font-size: 1rem;
  }
  
  .hero-content {
    padding: 0 var(--spacing-sm);
  }
}

/* Parallax effect for background */
@media (min-width: 769px) {
  .hero-background {
    transform: translateZ(0);
    will-change: transform;
  }
}
