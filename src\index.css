/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600;700&family=Lato:wght@300;400;500;600&display=swap');

:root {
  /* Color Scheme */
  --color-primary-green: #2F6A48;
  --color-accent-yellow: #F9C74F;
  --color-cream-beige: #F4F1DE;
  --color-dark-gray: #3A3A3A;
  --color-white: #FFFFFF;
  --color-light-green: #4A8B6B;
  --color-dark-green: #1F4A32;

  /* Typography */
  --font-serif: 'Playfair Display', serif;
  --font-sans: 'Lato', sans-serif;

  /* Spacing */
  --spacing-xs: 0.5rem;
  --spacing-sm: 1rem;
  --spacing-md: 1.5rem;
  --spacing-lg: 2rem;
  --spacing-xl: 3rem;
  --spacing-2xl: 4rem;

  /* Border Radius */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);

  /* Transitions */
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-sans);
  font-weight: 400;
  line-height: 1.6;
  color: var(--color-dark-gray);
  background-color: var(--color-white);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-serif);
  font-weight: 600;
  line-height: 1.2;
  color: var(--color-primary-green);
  margin-bottom: var(--spacing-sm);
}

h1 {
  font-size: 3.5rem;
  font-weight: 700;
}

h2 {
  font-size: 2.5rem;
}

h3 {
  font-size: 2rem;
}

h4 {
  font-size: 1.5rem;
}

p {
  margin-bottom: var(--spacing-sm);
  line-height: 1.7;
}

/* Links */
a {
  color: var(--color-primary-green);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--color-light-green);
}

/* Buttons */
.btn {
  display: inline-block;
  padding: var(--spacing-sm) var(--spacing-lg);
  font-family: var(--font-sans);
  font-weight: 500;
  text-align: center;
  text-decoration: none;
  border: none;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-normal);
  font-size: 1rem;
}

.btn-primary {
  background-color: var(--color-accent-yellow);
  color: var(--color-dark-gray);
}

.btn-primary:hover {
  background-color: #F7B32B;
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background-color: var(--color-primary-green);
  color: var(--color-white);
}

.btn-secondary:hover {
  background-color: var(--color-light-green);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

/* Section spacing */
.section {
  padding: var(--spacing-2xl) 0;
}

/* Utility classes */
.text-center {
  text-align: center;
}

.text-yellow {
  color: var(--color-accent-yellow);
}

.bg-green {
  background-color: var(--color-primary-green);
}

.bg-cream {
  background-color: var(--color-cream-beige);
}

/* Responsive design */
@media (max-width: 768px) {
  h1 {
    font-size: 2.5rem;
  }

  h2 {
    font-size: 2rem;
  }

  .container {
    padding: 0 var(--spacing-sm);
  }

  .section {
    padding: var(--spacing-xl) 0;
  }
}
