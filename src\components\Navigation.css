.navigation {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: var(--color-black);
  transition: all var(--transition-normal);
  border-bottom: 2px solid var(--color-accent-yellow);
}

.navigation.scrolled {
  background-color: var(--color-black);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 70px;
}

.nav-logo h2 {
  font-family: var(--font-display);
  font-size: 1.8rem;
  font-weight: 400;
  color: var(--color-accent-yellow);
  margin: 0;
  transition: color var(--transition-fast);
  letter-spacing: 1px;
}

.nav-logo:hover h2 {
  color: var(--color-white);
}

/* Desktop Menu */
.desktop-menu {
  display: flex;
  list-style: none;
  align-items: center;
  gap: var(--spacing-lg);
}

.nav-item {
  position: relative;
}

.nav-link {
  font-family: var(--font-heading);
  font-weight: 500;
  color: var(--color-white);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  gap: 0.25rem;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.nav-link:hover,
.nav-link.active {
  color: var(--color-accent-yellow);
  background-color: rgba(249, 199, 79, 0.1);
}

/* Dropdown */
.dropdown {
  position: relative;
}

.dropdown-toggle {
  background: none !important;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background-color: var(--color-white);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  padding: var(--spacing-sm) 0;
  min-width: 180px;
  list-style: none;
  border: 1px solid rgba(47, 106, 72, 0.1);
  animation: dropdownFadeIn 0.2s ease-out;
}

@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dropdown-link {
  display: block;
  padding: var(--spacing-xs) var(--spacing-md);
  color: var(--color-dark-gray);
  font-weight: 400;
  transition: all var(--transition-fast);
}

.dropdown-link:hover {
  background-color: var(--color-cream-beige);
  color: var(--color-primary-green);
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
  display: none;
  background: none;
  border: none;
  color: var(--color-primary-green);
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
  transition: background-color var(--transition-fast);
}

.mobile-menu-toggle:hover {
  background-color: rgba(47, 106, 72, 0.05);
}

/* Mobile Menu */
.mobile-menu {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: var(--color-white);
  border-bottom: 1px solid rgba(47, 106, 72, 0.1);
  box-shadow: var(--shadow-md);
  flex-direction: column;
  padding: var(--spacing-md) 0;
  list-style: none;
}

.mobile-menu.open {
  display: flex;
}

.mobile-menu .nav-item {
  width: 100%;
}

.mobile-menu .nav-link {
  padding: var(--spacing-sm) var(--spacing-md);
  justify-content: space-between;
  width: 100%;
}

.mobile-menu .dropdown-menu {
  position: static;
  box-shadow: none;
  border: none;
  background-color: var(--color-cream-beige);
  margin: 0;
  border-radius: 0;
}

.mobile-menu .dropdown-link {
  padding-left: var(--spacing-xl);
}

/* Responsive Design */
@media (max-width: 768px) {
  .desktop-menu {
    display: none;
  }

  .mobile-menu-toggle {
    display: block;
  }

  .nav-container {
    padding: 0 var(--spacing-sm);
  }

  .nav-logo h2 {
    font-size: 1.5rem;
  }
}

/* Add top padding to main content to account for fixed navigation */
main {
  padding-top: 70px;
}
