.about-section {
  background-color: var(--color-cream-beige);
}

.about-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-2xl);
  align-items: center;
}

.about-text h2 {
  font-size: 2.5rem;
  margin-bottom: var(--spacing-lg);
  color: var(--color-primary-green);
}

.handwritten-style {
  font-family: 'Brush Script MT', cursive;
  font-size: 1.3rem;
  color: var(--color-accent-yellow);
  margin-bottom: var(--spacing-lg);
  font-style: italic;
}

.about-text p {
  font-size: 1.1rem;
  line-height: 1.8;
  margin-bottom: var(--spacing-md);
  color: var(--color-dark-gray);
}

.image-collage {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-sm);
  height: 400px;
}

.image-placeholder {
  background: linear-gradient(135deg, var(--color-primary-green), var(--color-light-green));
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-white);
  font-weight: 500;
  text-align: center;
  box-shadow: var(--shadow-md);
  transition: transform var(--transition-normal);
}

.image-placeholder:hover {
  transform: scale(1.02);
}

.image-1 {
  grid-column: 1 / 2;
  grid-row: 1 / 3;
}

.image-2 {
  grid-column: 2 / 3;
  grid-row: 1 / 2;
}

.image-3 {
  grid-column: 2 / 3;
  grid-row: 2 / 3;
}

.image-4 {
  grid-column: 1 / 3;
  grid-row: 3 / 4;
  height: 120px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .about-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
  }
  
  .about-text {
    order: 1;
  }
  
  .about-images {
    order: 2;
  }
  
  .image-collage {
    height: 300px;
    grid-template-columns: 1fr;
    grid-template-rows: repeat(4, 1fr);
  }
  
  .image-1,
  .image-2,
  .image-3,
  .image-4 {
    grid-column: 1;
  }
  
  .image-1 {
    grid-row: 1;
  }
  
  .image-2 {
    grid-row: 2;
  }
  
  .image-3 {
    grid-row: 3;
  }
  
  .image-4 {
    grid-row: 4;
    height: auto;
  }
}
