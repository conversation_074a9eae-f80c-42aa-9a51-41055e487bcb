.events-preview {
  background-color: var(--color-primary-green);
  color: var(--color-white);
}

.events-preview .section-header h2 {
  color: var(--color-white);
}

.events-preview .section-header p {
  color: var(--color-cream-beige);
  opacity: 0.9;
}

.events-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-2xl);
}

.event-card {
  background-color: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all var(--transition-normal);
}

.event-card:hover {
  transform: translateY(-5px);
  background-color: rgba(255, 255, 255, 0.15);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.event-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  background-color: var(--color-accent-yellow);
  color: var(--color-dark-gray);
  border-radius: 50%;
  margin-bottom: var(--spacing-md);
}

.event-info h3 {
  font-size: 1.4rem;
  margin-bottom: var(--spacing-sm);
  color: var(--color-white);
}

.event-info p {
  color: var(--color-cream-beige);
  margin-bottom: var(--spacing-md);
  line-height: 1.6;
}

.event-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
  font-size: 0.9rem;
}

.event-date {
  color: var(--color-accent-yellow);
  font-weight: 500;
}

.event-time {
  color: var(--color-cream-beige);
}

.event-rsvp {
  font-size: 0.9rem;
  padding: 0.5rem 1rem;
}

.event-rsvp:hover {
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .events-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }
  
  .event-card {
    max-width: 400px;
    margin: 0 auto;
  }
  
  .event-details {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}
