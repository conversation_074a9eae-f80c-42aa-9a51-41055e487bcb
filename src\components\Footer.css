.footer {
  background-color: var(--color-dark-gray);
  color: var(--color-white);
  padding: var(--spacing-2xl) 0 var(--spacing-lg);
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
}

.footer-section h3,
.footer-section h4 {
  color: var(--color-white);
  margin-bottom: var(--spacing-md);
}

.footer-logo {
  font-family: var(--font-serif);
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--color-accent-yellow);
}

.footer-description {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin-bottom: var(--spacing-md);
}

.social-links {
  display: flex;
  gap: var(--spacing-sm);
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: var(--color-primary-green);
  color: var(--color-white);
  border-radius: 50%;
  transition: all var(--transition-normal);
}

.social-link:hover {
  background-color: var(--color-accent-yellow);
  color: var(--color-dark-gray);
  transform: translateY(-2px);
}

.footer-links {
  list-style: none;
}

.footer-links li {
  margin-bottom: var(--spacing-xs);
}

.footer-links a {
  color: rgba(255, 255, 255, 0.8);
  transition: color var(--transition-fast);
}

.footer-links a:hover {
  color: var(--color-accent-yellow);
}

.contact-info {
  margin-bottom: var(--spacing-md);
}

.contact-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-sm);
  color: rgba(255, 255, 255, 0.8);
}

.contact-item svg {
  margin-top: 2px;
  flex-shrink: 0;
  color: var(--color-accent-yellow);
}

.hours h5 {
  color: var(--color-accent-yellow);
  margin-bottom: var(--spacing-xs);
  font-size: 1rem;
}

.hours p {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
}

.newsletter-form {
  display: flex;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-sm);
}

.newsletter-input {
  flex: 1;
  padding: var(--spacing-sm);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: var(--radius-sm);
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--color-white);
  font-family: var(--font-sans);
}

.newsletter-input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.newsletter-input:focus {
  outline: none;
  border-color: var(--color-accent-yellow);
  background-color: rgba(255, 255, 255, 0.15);
}

.newsletter-btn {
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: 0.9rem;
  white-space: nowrap;
}

.newsletter-success {
  color: var(--color-accent-yellow);
  font-size: 0.9rem;
  font-weight: 500;
}

.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  padding-top: var(--spacing-lg);
}

.footer-bottom-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.9rem;
}

.footer-legal {
  display: flex;
  gap: var(--spacing-md);
}

.footer-legal a {
  color: rgba(255, 255, 255, 0.6);
  transition: color var(--transition-fast);
}

.footer-legal a:hover {
  color: var(--color-accent-yellow);
}

/* Responsive Design */
@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }
  
  .newsletter-form {
    flex-direction: column;
  }
  
  .newsletter-btn {
    align-self: flex-start;
  }
  
  .footer-bottom-content {
    flex-direction: column;
    gap: var(--spacing-sm);
    text-align: center;
  }
  
  .footer-legal {
    justify-content: center;
  }
}
