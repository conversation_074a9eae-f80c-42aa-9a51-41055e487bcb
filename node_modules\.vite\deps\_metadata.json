{"hash": "79fb7c86", "configHash": "ab5e1374", "lockfileHash": "11f0ed74", "browserHash": "18613c3b", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "b857d5b3", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "3fadaa5a", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "b40e1132", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "3efd6607", "needsInterop": true}, "framer-motion": {"src": "../../framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "da0e755a", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "97e62dee", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "b75b3012", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "c13cc9b4", "needsInterop": false}}, "chunks": {"chunk-I5HANLAN": {"file": "chunk-I5HANLAN.js"}, "chunk-ZTDXEEBU": {"file": "chunk-ZTDXEEBU.js"}, "chunk-X4QARNC5": {"file": "chunk-X4QARNC5.js"}}}