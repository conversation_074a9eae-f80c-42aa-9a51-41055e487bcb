.menu-preview {
  background-color: var(--color-white);
}

.section-header {
  margin-bottom: var(--spacing-2xl);
}

.section-header h2 {
  margin-bottom: var(--spacing-sm);
}

.section-header p {
  font-size: 1.2rem;
  color: var(--color-dark-gray);
  opacity: 0.8;
}

.featured-items {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-2xl);
}

.menu-item-card {
  background-color: var(--color-white);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  border: 1px solid rgba(47, 106, 72, 0.1);
}

.menu-item-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.item-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.image-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--color-primary-green), var(--color-light-green));
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-white);
  font-weight: 500;
  font-size: 1.1rem;
}

.special-badge,
.vegan-badge {
  position: absolute;
  top: var(--spacing-sm);
  right: var(--spacing-sm);
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm);
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.special-badge {
  background-color: var(--color-accent-yellow);
  color: var(--color-dark-gray);
}

.vegan-badge {
  background-color: var(--color-light-green);
  color: var(--color-white);
  top: calc(var(--spacing-sm) + 30px);
}

.item-info {
  padding: var(--spacing-md);
}

.item-info h3 {
  font-size: 1.3rem;
  margin-bottom: var(--spacing-xs);
  color: var(--color-primary-green);
}

.item-info p {
  color: var(--color-dark-gray);
  opacity: 0.8;
  margin-bottom: var(--spacing-sm);
  line-height: 1.6;
}

.item-price {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--color-accent-yellow);
  text-align: right;
}

/* Responsive Design */
@media (max-width: 768px) {
  .featured-items {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }
  
  .menu-item-card {
    max-width: 400px;
    margin: 0 auto;
  }
}
