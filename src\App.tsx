import React from 'react';
import { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';
import './App.css';

// Simple test navigation
const SimpleNav = () => (
  <nav style={{
    background: '#2F6A48',
    padding: '1rem',
    position: 'fixed',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1000
  }}>
    <div style={{ display: 'flex', gap: '2rem', alignItems: 'center' }}>
      <h2 style={{ color: '#F9C74F', margin: 0 }}>Indie Café</h2>
      <div style={{ display: 'flex', gap: '1rem' }}>
        <Link to="/" style={{ color: 'white', textDecoration: 'none' }}>Home</Link>
        <Link to="/menu" style={{ color: 'white', textDecoration: 'none' }}>Menu</Link>
        <Link to="/about" style={{ color: 'white', textDecoration: 'none' }}>About</Link>
        <Link to="/events" style={{ color: 'white', textDecoration: 'none' }}>Events</Link>
        <Link to="/contact" style={{ color: 'white', textDecoration: 'none' }}>Contact</Link>
      </div>
    </div>
  </nav>
);

// Simple test pages
const TestHomePage = () => <div style={{ padding: '100px 20px' }}><h1>Home Page</h1><p>This is the home page</p></div>;
const TestMenuPage = () => <div style={{ padding: '100px 20px' }}><h1>Menu Page</h1><p>This is the menu page</p></div>;
const TestAboutPage = () => <div style={{ padding: '100px 20px' }}><h1>About Page</h1><p>This is the about page</p></div>;
const TestEventsPage = () => <div style={{ padding: '100px 20px' }}><h1>Events Page</h1><p>This is the events page</p></div>;
const TestContactPage = () => <div style={{ padding: '100px 20px' }}><h1>Contact Page</h1><p>This is the contact page</p></div>;

function App() {
  return (
    <Router>
      <div className="App">
        <SimpleNav />
        <main>
          <Routes>
            <Route path="/" element={<TestHomePage />} />
            <Route path="/menu" element={<TestMenuPage />} />
            <Route path="/about" element={<TestAboutPage />} />
            <Route path="/events" element={<TestEventsPage />} />
            <Route path="/contact" element={<TestContactPage />} />
          </Routes>
        </main>
      </div>
    </Router>
  );
}

export default App;
