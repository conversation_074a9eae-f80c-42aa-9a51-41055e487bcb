import React from 'react';
import { Link } from 'react-router-dom';
import './MenuPreview.css';

// Define MenuItem type locally
interface MenuItem {
  id: string;
  name: string;
  description: string;
  price: number;
  category: string;
  image?: string;
  isVegan?: boolean;
  isGlutenFree?: boolean;
  isSpecial?: boolean;
}

const MenuPreview: React.FC = () => {
  // Sample menu items - indie café style
  const featuredItems: MenuItem[] = [
    {
      id: '1',
      name: 'THE INDIE BLEND',
      description: 'Our signature house roast - bold, uncompromising, locally sourced',
      price: 3.50,
      category: 'coffee',
      isSpecial: true,
    },
    {
      id: '2',
      name: 'LOADED BAGEL',
      description: 'Fresh bagel with cream cheese, avocado, and everything seasoning',
      price: 5.50,
      category: 'breakfast',
    },
    {
      id: '3',
      name: 'COMMUNITY NACHOS',
      description: 'Perfect for sharing - loaded with cheese, jalapeños, and salsa',
      price: 7.50,
      category: 'lunch',
      isSpecial: true,
    },
  ];

  return (
    <section className="menu-preview section">
      <div className="container">
        <div className="section-header text-center">
          <h2>FOOD & DRINK</h2>
          <p>Quality food and drink in a relaxed environment</p>
        </div>
        
        <div className="featured-items">
          {featuredItems.map((item) => (
            <div key={item.id} className="menu-item-card">
              <div className="item-image">
                <div className="image-placeholder">{item.name}</div>
                {item.isSpecial && <span className="special-badge">Daily Special</span>}
                {item.isVegan && <span className="vegan-badge">Vegan</span>}
              </div>
              <div className="item-info">
                <h3>{item.name}</h3>
                <p>{item.description}</p>
                <div className="item-price">${item.price.toFixed(2)}</div>
              </div>
            </div>
          ))}
        </div>
        
        <div className="text-center">
          <Link to="/menu" className="btn btn-secondary">
            SEE THE MENUS
          </Link>
        </div>
      </div>
    </section>
  );
};

export default MenuPreview;
