.hero-section {
  position: relative;
  height: calc(100vh - 70px);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--color-black);
  /* More indie/gritty background with texture */
  background-image:
    radial-gradient(circle at 25% 25%, rgba(249, 199, 79, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(47, 106, 72, 0.1) 0%, transparent 50%),
    linear-gradient(45deg, transparent 40%, rgba(255,255,255,0.02) 50%, transparent 60%);
  background-size: 400px 400px, 300px 300px, 100px 100px;
  background-position: 0 0, 100px 100px, 0 0;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
}

.hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
  color: var(--color-white);
  max-width: 800px;
  padding: 0 var(--spacing-md);
}

.hero-title {
  font-family: var(--font-display);
  font-size: 5rem;
  font-weight: 400;
  margin-bottom: var(--spacing-sm);
  color: var(--color-white);
  text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.7);
  line-height: 0.9;
  letter-spacing: 2px;
}

.hero-subtitle {
  font-family: var(--font-heading);
  font-size: 1.8rem;
  font-weight: 500;
  margin-bottom: var(--spacing-md);
  color: var(--color-accent-yellow);
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  letter-spacing: 3px;
}

.hero-description {
  font-size: 1.1rem;
  font-weight: 400;
  margin-bottom: var(--spacing-xl);
  color: var(--color-cream-beige);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
}

.hero-buttons {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  flex-wrap: wrap;
}

.hero-cta {
  font-family: var(--font-heading);
  font-size: 1rem;
  padding: var(--spacing-md) var(--spacing-xl);
  box-shadow: var(--shadow-lg);
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 600;
  border: 2px solid transparent;
}

.hero-cta.btn-primary {
  background-color: var(--color-accent-yellow);
  color: var(--color-black);
}

.hero-cta.btn-secondary {
  background-color: transparent;
  color: var(--color-white);
  border-color: var(--color-white);
}

.hero-cta:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
}

.hero-cta.btn-secondary:hover {
  background-color: var(--color-white);
  color: var(--color-black);
}

/* Decorative steam animation */
.hero-decoration {
  position: absolute;
  bottom: 20%;
  right: 10%;
  z-index: 1;
}

.steam-line {
  position: absolute;
  width: 3px;
  height: 40px;
  background: linear-gradient(
    to top,
    transparent 0%,
    rgba(255, 255, 255, 0.6) 50%,
    transparent 100%
  );
  border-radius: 2px;
}

.steam-1 {
  left: 0;
}

.steam-2 {
  left: 8px;
}

.steam-3 {
  left: 16px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }
  
  .hero-subtitle {
    font-size: 1.2rem;
  }
  
  .hero-cta {
    font-size: 1rem;
    padding: var(--spacing-sm) var(--spacing-lg);
  }
  
  .hero-decoration {
    display: none;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 2rem;
  }
  
  .hero-subtitle {
    font-size: 1rem;
  }
  
  .hero-content {
    padding: 0 var(--spacing-sm);
  }
}

/* Parallax effect for background */
@media (min-width: 769px) {
  .hero-background {
    transform: translateZ(0);
    will-change: transform;
  }
}
