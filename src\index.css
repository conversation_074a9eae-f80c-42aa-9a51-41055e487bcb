/* Import Google Fonts - More indie/gritty fonts */
@import url('https://fonts.googleapis.com/css2?family=Oswald:wght@300;400;500;600;700&family=Source+Sans+Pro:wght@300;400;600;700&family=Bebas+Neue&display=swap');

:root {
  /* Color Scheme - More indie/gritty */
  --color-primary-green: #2F6A48;
  --color-accent-yellow: #F9C74F;
  --color-cream-beige: #F4F1DE;
  --color-dark-gray: #424242;
  --color-white: #FFFFFF;
  --color-light-green: #4A8B6B;
  --color-dark-green: #1F4A32;
  --color-black: #000000;
  --color-off-white: #FAFAFA;

  /* Typography - More indie fonts */
  --font-display: 'Bebas Neue', cursive;
  --font-heading: 'Oswald', sans-serif;
  --font-body: 'Source Sans Pro', sans-serif;

  /* Spacing */
  --spacing-xs: 0.5rem;
  --spacing-sm: 1rem;
  --spacing-md: 1.5rem;
  --spacing-lg: 2rem;
  --spacing-xl: 3rem;
  --spacing-2xl: 4rem;

  /* Border Radius */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);

  /* Transitions */
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-body);
  font-weight: 400;
  line-height: 1.6;
  color: var(--color-dark-gray);
  background-color: var(--color-off-white);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin: 0;
  padding: 0;
}

/* Typography - More indie/bold */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-heading);
  font-weight: 600;
  line-height: 1.1;
  color: var(--color-dark-gray);
  margin-bottom: var(--spacing-sm);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

h1 {
  font-size: 4rem;
  font-weight: 700;
  font-family: var(--font-display);
  letter-spacing: 1px;
}

h2 {
  font-size: 2.8rem;
  font-weight: 600;
}

h3 {
  font-size: 2.2rem;
  font-weight: 500;
}

h4 {
  font-size: 1.6rem;
}

p {
  margin-bottom: var(--spacing-sm);
  line-height: 1.7;
}

/* Links */
a {
  color: var(--color-primary-green);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--color-light-green);
}

/* Buttons - More indie style */
.btn {
  display: inline-block;
  padding: var(--spacing-sm) var(--spacing-lg);
  font-family: var(--font-heading);
  font-weight: 600;
  text-align: center;
  text-decoration: none;
  border: 2px solid transparent;
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all var(--transition-normal);
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.btn-primary {
  background-color: var(--color-accent-yellow);
  color: var(--color-black);
  border-color: var(--color-accent-yellow);
}

.btn-primary:hover {
  background-color: var(--color-black);
  color: var(--color-accent-yellow);
  border-color: var(--color-accent-yellow);
  transform: translateY(-2px);
}

.btn-secondary {
  background-color: var(--color-black);
  color: var(--color-white);
  border-color: var(--color-black);
}

.btn-secondary:hover {
  background-color: var(--color-white);
  color: var(--color-black);
  border-color: var(--color-black);
  transform: translateY(-2px);
}

/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

/* Section spacing */
.section {
  padding: var(--spacing-2xl) 0;
}

/* Utility classes */
.text-center {
  text-align: center;
}

.text-yellow {
  color: var(--color-accent-yellow);
}

.bg-green {
  background-color: var(--color-primary-green);
}

.bg-cream {
  background-color: var(--color-cream-beige);
}

/* Responsive design */
@media (max-width: 768px) {
  h1 {
    font-size: 2.5rem;
  }

  h2 {
    font-size: 2rem;
  }

  .container {
    padding: 0 var(--spacing-sm);
  }

  .section {
    padding: var(--spacing-xl) 0;
  }
}
